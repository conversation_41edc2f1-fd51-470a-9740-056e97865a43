#!/usr/bin/env python3
"""
参考任务决策执行Agent

融合决策和执行的Agent，直接调用vision模型输出坐标并执行Android操作
在prompt中包含参考任务的历史和图片
"""

import json
import re
import time
from datetime import datetime
from typing import Dict, Any, Tuple

from langchain.output_parsers import OutputFixingParser
from langchain_core.output_parsers import PydanticOutputParser
from loguru import logger
from pydantic import BaseModel, Field

from src.domain.reference_task.prompt.reference_decision_prompt import build_three_step_reference_decision_prompt, \
    system_invoke_prompt, _is_step_match
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.domain.ui_task.mobile.android.action_tool import (
    execute_simple_action
)
from src.domain.ui_task.mobile.android.image_processor import image_annotator
from src.domain.ui_task.mobile.android.screenshot_manager import convert_screenshot_to_base64, screenshot_manager
from src.domain.ui_task.mobile.utils.exception_handler import TaskExceptionHandler
from src.infra.model import get_chat_model


class ReferenceDecisionResponse(BaseModel):
    """参考任务决策Agent响应的数据模型"""
    self_check: str = Field(default="", description="自检流程的自检结果")
    interface_analysis: str = Field(default="", description="当前界面分析")
    reference_analysis: str = Field(default="", description="参考任务分析")
    current_step_name: str = Field(default="", description="当前正在执行的步骤名称，从测试用例信息获取")
    action_decision: str = Field(default="", description="动作决策结果")
    instruction: str = Field(default="", description="操作指令")
    action: str = Field(default="", description="具体动作命令")


class ReferenceDecisionAgent:
    """参考任务决策执行Agent - 融合决策和执行"""

    def __init__(self):
        """初始化决策执行Agent"""
        self.model = get_chat_model(model_name="copy")

        # 创建JSON解析器
        self.pydantic_parser = PydanticOutputParser(pydantic_object=ReferenceDecisionResponse)

        # 尝试使用专门的修复模型，如果不存在则使用默认模型
        self.output_parser = OutputFixingParser.from_llm(
            parser=self.pydantic_parser,
            llm=get_chat_model(model_name="fix")
        )

    def execute_step_with_reference(
            self,
            state: DeploymentState,
            step_description: str,
            max_step_attempts: int = 7
    ) -> str:
        """
        参照成功案例执行单个步骤，类似分步执行模式

        Args:
            state: 参考任务状态
            step_description: 步骤描述
            max_step_attempts: 单个步骤最大尝试次数，默认7次

        Returns:
            "finished": 步骤完成
            "failed": 步骤失败
            "continue": 继续执行
        """
        task_id = state["task_id"]
        device_id = state["device"]

        # 检查当前步骤的执行次数
        current_step_index = state.get("current_step_index", 0)
        step_attempt_count = self._get_step_attempt_count(state, current_step_index)

        # 计算动态最大执行次数：成功案例轮数 + 7次
        reference_case_count = self._get_reference_case_count(state, step_description)
        dynamic_max_attempts = reference_case_count + 7
        actual_max_attempts = max(dynamic_max_attempts, max_step_attempts)  # 至少保证原有的最大次数
        logger.info(f"[{task_id}] 🔄 Step attempt {step_attempt_count + 1}/{actual_max_attempts}")

        if step_attempt_count > actual_max_attempts:
            error_msg = f"步骤执行次数超过最大限制({actual_max_attempts}次，成功案例{reference_case_count}轮+7次)，主动退出"
            logger.error(f"[{task_id}] ❌ {error_msg}")
            # 创建并完成动作记录（失败）
            current_action = self._create_step_action_record(task_id, step_description)
            self._complete_step_action_record(current_action, False, error_msg)
            # 记录失败的执行历史
            self._record_step_execution_history(state, step_description, error_msg, "failed", "failed", "failed")
            return "failed"

        logger.info(f"[{task_id}] 🔄 Step attempt {step_attempt_count + 1}/{max_step_attempts}")

        # 创建步骤动作记录
        current_action = self._create_step_action_record(task_id, step_description)

        try:
            logger.info(f"[{task_id}] 🧠 Reference decision-execution agent analyzing step: {step_description}")
            time.sleep(3)

            # 在拼接prompt前重新截图以获取最新界面状态
            logger.info(f"[{task_id}] 📸 Taking fresh screenshot before decision...")
            screenshot_path = screenshot_manager.take_screenshot(
                device=device_id,
                task_id=task_id,
                execution_count=state.get("execution_count", 0) + 1
            )
            # 转换截图为base64
            current_image_base64 = convert_screenshot_to_base64(screenshot_path, task_id)

            # 构建包含参考任务信息和当前步骤的prompt
            messages = self._build_step_reference_messages(state, current_image_base64, step_description)

            start_time = time.time()
            # 使用langchain的chat model调用
            model_response_obj = self.model.invoke(messages)
            model_response = model_response_obj.content
            end_time = time.time()

            logger.info(f"[{task_id}] 🧠 Reference model response time: {end_time - start_time:.2f}s")
            logger.info(f"[{task_id}] 🧠 Reference model response: \n{model_response}")

            # 解析JSON响应
            parsed_fields, action_command = self._parse_json_response(model_response, task_id)

            # 更新动作记录的决策内容和动作命令
            self._update_action_record_content(current_action, parsed_fields, action_command, screenshot_path)

            # 记录决策日志
            self._log_decision_immediately(parsed_fields, action_command, task_id)

            # 检查是否需要切换步骤（根据current_step_name）
            current_step_name = parsed_fields.get("current_step_name", "")
            if current_step_name:
                step_switched = self._check_and_switch_step(state, current_step_name, task_id)
                if step_switched:
                    logger.info(f"[{task_id}] 🔄 Step switched to: {current_step_name}")

            # 检查是否是finished或failed状态
            if action_command.strip() == "finished()" or action_command.strip().startswith("finished("):
                logger.info(f"[{task_id}] ✅ Step marked as finished by AI")
                # 完成动作记录（成功）
                self._complete_step_action_record(current_action, True, "步骤完成")
                # 记录成功完成的步骤执行历史
                self._record_step_execution_history(state, step_description, model_response, action_command, "finished",
                                                    screenshot_path)
                return "finished"
            elif action_command.strip().startswith("failed("):
                logger.error(f"[{task_id}] ❌ Step marked as failed by AI")
                # 完成动作记录（失败）
                self._complete_step_action_record(current_action, False, "步骤失败")
                # 记录失败的步骤执行历史
                self._record_step_execution_history(state, step_description, model_response, action_command, "failed",
                                                    screenshot_path)
                return "failed"

            # 执行动作
            execution_result = self._execute_action_command(action_command, device_id, task_id)

            # 执行完成后进行截图标注
            self._annotate_screenshot_after_execution(state, action_command, screenshot_path, execution_result)

            # 只有执行成功时才记录到状态历史和步骤执行历史
            if execution_result.get("status") == "success":
                self._record_execution_result(state, parsed_fields, execution_result, screenshot_path)
                # 记录成功的步骤执行历史
                self._record_step_execution_history(state, step_description, model_response, action_command, "continue",
                                                    screenshot_path)
                # 完成动作记录（成功）
                self._complete_step_action_record(current_action, True, "动作执行成功")
                logger.info(f"[{task_id}] ✅ Execution result recorded to history")
                return "continue"
            else:
                logger.warning(
                    f"[{task_id}] ⚠️ Execution failed (likely due to action parsing error), not recording to history to avoid contaminating next round")
                # 完成动作记录（失败）
                self._complete_step_action_record(current_action, False, "动作执行失败")
                return "continue"

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Reference decision-execution agent failed: {str(e)}")
            # 完成动作记录（失败）
            self._complete_step_action_record(current_action, False, f"执行异常: {str(e)}")
            return "failed"

    @staticmethod
    def _preprocess_json_string(json_str: str) -> str:
        """
        预处理JSON字符串，修复常见的转义字符问题

        Args:
            json_str: 原始JSON字符串

        Returns:
            修复后的JSON字符串
        """
        # 修复无效的单引号转义 \' -> '
        # 在JSON中，单引号不需要转义，只有双引号需要转义
        cleaned = json_str.replace("\\'", "'")

        # 其他可能的修复...
        # 如果需要，可以在这里添加更多的修复逻辑

        return cleaned

    @staticmethod
    def _build_step_reference_messages(
            state: DeploymentState,
            current_image_base64: str,
            step_description: str
    ) -> list:
        """
        构建包含参考任务信息和当前步骤的消息列表，用于分步执行

        Args:
            state: 参考任务状态
            current_image_base64: 当前截图base64
            step_description: 当前步骤描述

        Returns:
            消息列表
        """
        # 构建系统prompt，使用新的三步骤逻辑
        system_prompt = build_three_step_reference_decision_prompt(state, step_description)
        messages = [{
            "role": "user",
            "content": system_prompt
        }, {
            "role": "user",
            "content": system_invoke_prompt()
        }, {
            "role": "user",
            "content": "########## 成功案例截图 ##########"
        }]

        # 添加参考任务当前步骤的所有轮截图
        step_screenshots = ReferenceDecisionAgent._get_step_reference_screenshots(
            state.get("reference_task_id", ""),
            step_description
        )

        count = 1
        for i, screenshot_base64 in enumerate(step_screenshots, 1):
            count += i
            messages.append({
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": f"成功案例第{i}轮截图"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{screenshot_base64}",
                            "detail": "high"
                        }
                    }
                ]
            })

        # 添加成功案例下一步骤第1轮截图和执行信息
        next_step_screenshot = ReferenceDecisionAgent._get_next_step_first_screenshot(
            state.get("reference_task_id", ""),
            state.get("task_steps", []),
            state.get("current_step_index", 0)
        )

        if next_step_screenshot:
            messages.append({
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": f"成功案例第{count}轮截图"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{next_step_screenshot}",
                            "detail": "high"
                        }
                    }
                ]
            })

        messages.append({
            "role": "user",
            "content": "########## 当前轮截图 ##########"
        })

        messages.append({
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{current_image_base64}",
                        "detail": "high"
                    }
                }
            ]
        })

        return messages

    def _parse_json_response(self, model_response: str, task_id: str) -> Tuple[Dict[str, Any], str]:
        """
        解析模型的JSON响应，提取各个字段
        使用OutputFixingParser来纠正格式错误的JSON

        Args:
            model_response: 模型的完整响应
            task_id: 任务ID

        Returns:
            Tuple[parsed_fields, action_command]: 解析的字段字典、动作命令

        Raises:
            Exception: 当JSON解析和修复都失败时抛出异常
        """
        try:
            # 预处理JSON字符串，修复常见的转义字符问题
            cleaned_response = self._preprocess_json_string(model_response.strip())

            # 首先尝试直接解析JSON
            json_data = json.loads(cleaned_response)
            logger.info(f"[{task_id}] ✅ Direct JSON parsing successful")

        except json.JSONDecodeError as e:
            logger.warning(f"[{task_id}] ⚠️ Direct JSON parsing failed: {str(e)}")
            logger.info(f"[{task_id}] 🔧 Attempting to fix JSON format using OutputFixingParser...")

            try:
                # 使用OutputFixingParser来修复JSON格式
                parsed_response: ReferenceDecisionResponse = self.output_parser.parse(model_response)
                json_data = parsed_response.model_dump()
                logger.info(f"[{task_id}] ✅ JSON format fixed successfully using OutputFixingParser")

            except Exception as fix_error:
                logger.error(f"[{task_id}] ❌ OutputFixingParser failed: {str(fix_error)}")
                logger.error(f"[{task_id}] Raw response: {model_response}")

                # 使用统一的异常处理方法
                TaskExceptionHandler.update_task_status_to_failed(
                    task_id,
                    f"JSON parsing and fixing failed in reference decision agent: {str(fix_error)}"
                )

                # 重新抛出异常，让外层异常处理机制处理
                raise fix_error

        # 构建返回的字段字典
        parsed_fields = {
            "self_check": json_data.get("self_check", ""),
            "interface_analysis": json_data.get("interface_analysis", ""),
            "current_step_name": json_data.get("current_step_name", ""),
            "action_decision": json_data.get("action_decision", ""),
            "instruction": json_data.get("instruction", ""),
            "action": json_data.get("action", "")
        }

        action_command = json_data.get("action", "")

        return parsed_fields, action_command

    @staticmethod
    def _get_reference_task_images(reference_task_id: str) -> list:
        """
        获取参考任务的关键截图

        Args:
            reference_task_id: 参考任务ID

        Returns:
            截图base64列表
        """
        try:
            # 获取参考任务的动作记录
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            actions = task_persistence_service.get_task_actions(reference_task_id)

            if not actions:
                return []

            images = []
            for action in actions:
                try:
                    # 转换截图为base64
                    img_base64 = convert_screenshot_to_base64(action.image_path, reference_task_id)
                    if img_base64:
                        images.append(img_base64)
                except Exception as e:
                    logger.warning(f"Failed to load reference image {action.image_path}: {str(e)}")
                    continue

            logger.info(f"Loaded {len(images)} reference images for task {reference_task_id}")
            return images

        except Exception as e:
            logger.error(f"Error getting reference task images: {str(e)}")
            return []

    @staticmethod
    def _get_step_reference_screenshots(reference_task_id: str, step_description: str) -> list:
        """
        获取参考任务中当前步骤的所有轮截图

        Args:
            reference_task_id: 参考任务ID
            step_description: 当前步骤描述

        Returns:
            当前步骤的截图base64列表
        """
        try:
            # 获取参考任务的动作记录
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            actions = task_persistence_service.get_task_actions(reference_task_id)

            if not actions:
                logger.info(f"No actions found for reference task {reference_task_id}")
                return []

            # 过滤出当前步骤的所有动作
            step_actions = []
            for action in actions:
                if _is_step_match(action.step_name, step_description):
                    step_actions.append(action)

            if not step_actions:
                logger.info(f"No matching actions found for step: {step_description}")
                return []

            # 获取这些动作的截图
            images = []
            for action in step_actions:
                try:
                    # 转换截图为base64
                    img_base64 = convert_screenshot_to_base64(action.image_path, reference_task_id)
                    if img_base64:
                        images.append(img_base64)
                except Exception as e:
                    logger.warning(f"Failed to load step reference image {action.image_path}: {str(e)}")
                    continue
            logger.info(
                f"Loaded {len(images)} step reference images for step '{step_description}' in task {reference_task_id}")
            return images

        except Exception as e:
            logger.error(f"Error loading step reference screenshots: {str(e)}")
            return []

    @staticmethod
    def _get_next_step_first_screenshot(reference_task_id: str, task_steps: list, current_step_index: int) -> str:
        """
        获取成功案例下一步骤的第1轮截图和对应的执行信息

        Args:
            reference_task_id: 参考任务ID
            task_steps: 任务步骤列表
            current_step_index: 当前步骤索引

        Returns:
            (screenshot_base64, text_info) 元组，如果没有则返回 ("", "")
        """
        try:
            # 检查是否有下一步骤
            next_step_index = current_step_index + 1
            if next_step_index >= len(task_steps):
                logger.info("Already at the last step, no next step screenshot needed")
                return ""

            next_step_description = task_steps[next_step_index]
            logger.info(f"Getting next step first screenshot and info for: {next_step_description}")

            # 获取参考任务的动作记录
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            actions = task_persistence_service.get_task_actions(reference_task_id)

            if not actions:
                logger.info(f"No actions found for reference task {reference_task_id}")
                return ""

            for action in actions:
                if _is_step_match(action.step_name, next_step_description):
                    screenshot_base64 = convert_screenshot_to_base64(action.image_path, reference_task_id)

                    if screenshot_base64:
                        logger.info(f"Successfully loaded next step info for: {next_step_description}")
                        return screenshot_base64

            logger.info(f"No screenshot found for next step: {next_step_description}")
            return ""

        except Exception as e:
            logger.error(f"Error loading next step first screenshot: {str(e)}")
            return ""

    @staticmethod
    def _execute_action_command(action_command: str, device_id: str, task_id: str) -> Dict[str, Any]:
        """
        执行动作命令，参考execution_agent的实现方式

        Args:
            action_command: 动作命令
            device_id: 设备ID
            task_id: 任务ID

        Returns:
            执行结果
        """
        try:
            logger.info(f"[{task_id}] 🎯 Executing action command: {action_command}")

            # 处理特殊动作（finished, failed）
            if action_command.strip() == "finished()" or action_command.strip().startswith("finished("):
                return {
                    "status": "success",
                    "action": "finished",
                    "message": "Task completed successfully"
                }

            if action_command.strip().startswith("failed("):
                # 提取失败原因
                reason = "Unknown error"
                if "content=" in action_command:
                    content_match = re.search(r"content='([^']*)'", action_command)
                    if content_match:
                        reason = content_match.group(1)

                return {
                    "status": "failed",
                    "action": "failed",
                    "message": f"Task failed: {reason}"
                }

            # 处理wait动作
            if action_command.strip().startswith("wait("):
                seconds_match = re.search(r"wait\(seconds=(\d+)\)", action_command)
                if seconds_match:
                    seconds = int(seconds_match.group(1))
                else:
                    seconds = 3  # 默认等待3秒

                time.sleep(seconds)
                return {
                    "status": "success",
                    "action": f"wait({seconds})",
                    "message": f"Waited for {seconds} seconds"
                }

            # 直接使用action_tool执行动作（与execution_agent保持一致）
            logger.info(f"[{task_id}] ⚡ Executing action with action_tool: {action_command}")
            result = execute_simple_action(action_command, device_id)

            logger.info(f"[{task_id}] 🎯 Action execution result: {result}")

            return result

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error executing action command: {str(e)}")
            return {
                "status": "error",
                "message": f"Execution error: {str(e)}",
                "action": action_command
            }

    @staticmethod
    def _log_decision_immediately(parsed_fields: Dict[str, Any], action_command: str, task_id: str):
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService

            step_name = parsed_fields.get("current_step_name", "")
            action_decision = parsed_fields.get("action_decision", "")

            decision_log = ExecutionLogService.create_decision_log(step_name, action_decision, action_command)
            task_persistence_service.append_execution_log_entries(task_id, [decision_log])
            logger.info(f"[{task_id}] 📝 Reference decision logged immediately")

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to log reference decision: {str(e)}")

    @staticmethod
    def _create_step_action_record(task_id: str, step_description: str):
        """
        创建步骤动作记录

        Args:
            task_id: 任务ID
            step_description: 步骤描述

        Returns:
            创建的动作记录对象
        """
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            current_action = task_persistence_service.create_task_action(
                task_id=task_id,
                step_name=step_description,
                action_command="",  # 稍后更新
                decision_content=""  # 稍后更新
            )

            return current_action

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Failed to create step action record: {str(e)}")
            return None

    @staticmethod
    def _complete_step_action_record(current_action, success: bool, message: str = None):
        """
        完成步骤动作记录

        Args:
            current_action: 动作记录对象
            success: 是否成功
            message: 完成消息
        """
        if not current_action:
            return

        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            task_persistence_service.complete_task_action(
                action_id=current_action.id,
                success=success,
                error_message=message if not success else None
            )

            status = "成功" if success else "失败"
            logger.info(f"Reference step action record completed: {status} - {message or ''}")

        except Exception as e:
            logger.error(f"Failed to complete reference step action record: {str(e)}")

    @staticmethod
    def _update_action_record_content(current_action, parsed_fields: dict, action_command: str, screenshot_path: str):
        """
        更新动作记录的内容

        Args:
            current_action: 动作记录对象
            parsed_fields: 解析的字段
            action_command: 动作命令
            screenshot_path: 截图路径
        """
        if not current_action:
            return

        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            # 构建完整的thought内容
            interface_analysis = parsed_fields.get("interface_analysis", "")
            action_decision = parsed_fields.get("action_decision", "")

            # 组合thought内容
            thought_content = ""
            if interface_analysis:
                thought_content += f"界面分析: {interface_analysis}\n"
            if action_decision:
                thought_content += f"决策内容: {action_decision}\n"
            if action_command:
                thought_content += f"执行动作: {action_command}"

            # 更新动作记录
            task_persistence_service.action_repo.update_action_extra_info(
                action_id=current_action.id,
                before_screenshot=screenshot_path,
                final_action_command=action_command
            )

            # 更新决策内容和动作命令
            current_action.decision_content = thought_content.strip()
            current_action.action = action_command

            logger.debug(f"Updated reference action record content for action {current_action.id}")

        except Exception as e:
            logger.error(f"Failed to update reference action record content: {str(e)}")

    @staticmethod
    def _annotate_screenshot_after_execution(
            state: DeploymentState,
            action_command: str,
            screenshot_path: str,
            execution_result: Dict[str, Any]
    ) -> None:
        """
        在动作执行完成后对截图进行标注

        Args:
            state: 参考任务状态
            action_command: 动作命令
            screenshot_path: 截图路径
            execution_result: 执行结果
        """
        task_id = state["task_id"]
        device_id = state["device"]

        # 跳过不需要标注的动作
        if not ReferenceDecisionAgent._requires_coordinates(action_command):
            return

        # 只有在动作执行成功时才进行标注
        if execution_result.get("status") != "success":
            logger.info(f"[{task_id}] ⚠️ Skipping annotation due to failed execution: {execution_result.get('status')}")
            return

        try:
            # 执行标注（直接在原文件上标注，不返回路径）
            image_annotator.annotate_screenshot_from_action(
                screenshot_path, action_command, task_id, device=device_id
            )
            logger.info(f"[{task_id}] 🎯 Screenshot annotated after reference execution: {action_command}")

        except Exception as e:
            logger.warning(f"[{task_id}] ⚠️ Failed to annotate screenshot after reference execution: {str(e)}")

    @staticmethod
    def _requires_coordinates(action_str: str) -> bool:
        """
        判断动作是否需要坐标（参考execution_agent的实现）

        Args:
            action_str: 动作字符串

        Returns:
            是否需要坐标
        """
        action_str_lower = action_str.lower()

        # 需要坐标的动作类型
        coordinate_actions = [
            'click', 'long_press', 'drag', 'scroll'
        ]

        # 不需要坐标的动作类型
        non_coordinate_actions = [
            'wait', 'back', 'type', 'delete', 'enter', 'finished', 'failed'
        ]

        # 检查是否包含需要坐标的动作
        for action in coordinate_actions:
            if action in action_str_lower:
                return True

        # 检查是否包含不需要坐标的动作
        for action in non_coordinate_actions:
            if action in action_str_lower:
                return False

        # 默认情况下，如果无法确定，返回False（不需要坐标）
        return False

    @staticmethod
    def _record_execution_result(
            state: DeploymentState,
            parsed_fields: Dict[str, Any],
            execution_result: Dict[str, Any],
            screenshot_path: str = None
    ):
        """
        记录执行结果到状态历史

        Args:
            state: 参考任务状态
            parsed_fields: 解析的字段
            execution_result: 执行结果
            screenshot_path: 截图路径
        """
        try:
            # 确保execution_count从1开始
            execution_count = state.get("execution_count", 0)
            if execution_count == 0:
                execution_count = 1

            # 将截图路径添加到执行结果中
            if screenshot_path:
                execution_result = execution_result.copy()
                execution_result["screenshot_path"] = screenshot_path

            history_entry = {
                "execution_count": execution_count,
                "parsed_fields": parsed_fields,
                "execution_result": execution_result,
                "timestamp": datetime.now().isoformat()
            }

            if "history" not in state:
                state["history"] = []

            state["history"].append(history_entry)
        except Exception as e:
            logger.error(f"Error recording execution result: {str(e)}")

    @staticmethod
    def _record_step_execution_history(
            state: DeploymentState,
            step_description: str,
            ai_response: str,
            action_command: str,
            step_status: str,
            screenshot_path: str = ""
    ):
        """
        记录步骤执行历史，参照execution_agent的实现

        Args:
            state: 参考任务状态
            step_description: 步骤描述
            ai_response: AI响应内容
            action_command: 执行的动作命令
            step_status: 步骤状态
            screenshot_path: 截图路径
        """
        from datetime import datetime

        current_step_index = state.get("current_step_index", 0)

        history_entry = {
            "action": "step_execution_with_reference",
            "step_index": current_step_index,
            "step_description": step_description,
            "ai_response": ai_response,
            "action_command": action_command,
            "step_status": step_status,
            "screenshot_path": screenshot_path,
            "timestamp": datetime.now().isoformat()
        }

        if "history" not in state:
            state["history"] = []
        state["history"].append(history_entry)

    @staticmethod
    def _escape_template_variables(text: str) -> str:
        """
        转义文本中的模板变量符号，防止LangChain将其识别为变量

        Args:
            text: 原始文本

        Returns:
            转义后的文本
        """
        if not text:
            return text

        # 将单个花括号转义为双花括号
        # 这样LangChain就不会将其识别为模板变量
        return text.replace("{", "{{").replace("}", "}}")

    @staticmethod
    def _check_and_switch_step(state: DeploymentState, current_step_name: str, task_id: str) -> bool:
        """
        检查并切换步骤，根据模型输出的current_step_name

        Args:
            state: 参考任务状态
            current_step_name: 模型输出的当前步骤名称
            task_id: 任务ID

        Returns:
            是否发生了步骤切换
        """
        try:
            task_steps = state.get("task_steps", [])
            current_step_index = state.get("current_step_index", 0)

            # 查找current_step_name在task_steps中的索引
            new_step_index = None
            for i, step in enumerate(task_steps):
                # 使用灵活的匹配逻辑
                if ReferenceDecisionAgent._is_step_name_match(step, current_step_name):
                    new_step_index = i
                    break

            if new_step_index is not None and new_step_index != current_step_index:
                # 发生步骤切换
                logger.info(f"[{task_id}] 🔄 Step switching from index {current_step_index} to {new_step_index}")
                logger.info(
                    f"[{task_id}] 🔄 Step switching from '{task_steps[current_step_index] if current_step_index < len(task_steps) else 'unknown'}' to '{task_steps[new_step_index]}'")

                # 更新状态中的当前步骤索引
                state["current_step_index"] = new_step_index

                return True

            return False

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error in step switching: {str(e)}")
            return False

    @staticmethod
    def _is_step_name_match(task_step: str, current_step_name: str) -> bool:
        """
        判断步骤名称是否匹配

        Args:
            task_step: 任务步骤（如 "1.点击底部消息tab"）
            current_step_name: 模型输出的步骤名称

        Returns:
            是否匹配
        """
        if not task_step or not current_step_name:
            return False

        # 清理字符串
        task_step_clean = task_step.strip()
        current_step_clean = current_step_name.strip()

        # 1. 完全匹配
        if task_step_clean == current_step_clean:
            return True

        # 2. 去除序号后匹配
        import re
        task_step_no_num = re.sub(r'^\d+[.)、\-\s]*', '', task_step_clean)
        current_step_no_num = re.sub(r'^\d+[.)、\-\s]*', '', current_step_clean)

        if task_step_no_num == current_step_no_num:
            return True

        # 3. 包含关系匹配（current_step_name包含在task_step中）
        if current_step_no_num in task_step_no_num or task_step_no_num in current_step_no_num:
            return True

        return False

    @staticmethod
    def _get_reference_case_count(state: DeploymentState, step_description: str) -> int:
        """
        获取指定步骤的成功案例轮数

        Args:
            state: 参考任务状态
            step_description: 步骤描述

        Returns:
            该步骤的成功案例轮数
        """
        reference_actions = state.get("reference_actions", [])

        # 统计当前步骤的成功案例数量
        step_reference_count = 0
        for action in reference_actions:
            action_step_name = action.get("step_name", "")
            if action_step_name and step_description:
                # 使用灵活的匹配逻辑
                if ReferenceDecisionAgent._is_step_name_match(action_step_name, step_description):
                    step_reference_count += 1

        return max(step_reference_count, 1)  # 至少返回1，避免除零错误

    @staticmethod
    def _get_step_attempt_count(state: DeploymentState, step_index: int) -> int:
        """
        获取指定步骤的尝试次数，参照execution_agent的实现

        Args:
            state: 参考任务状态
            step_index: 步骤索引

        Returns:
            该步骤的尝试次数
        """
        history = state.get("history", [])

        # 统计当前步骤的执行记录数量
        step_attempts = [r for r in history if
                         r.get("action") == "step_execution_with_reference" and
                         r.get("step_index") == step_index]

        return len(step_attempts)
