/Users/<USER>/Workspace/click-pilot/.venv/bin/python /Users/<USER>/Workspace/click-pilot/main.py
/Users/<USER>/Workspace/click-pilot/.venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:198: UserWarning: Field name "copy" in "ModelSettings" shadows an attribute in parent "BaseModel"
  warnings.warn(
2025-08-28 14:21:53.244 | INFO     | src.infra.web.http:_setup_cors:56 - ✅ CORS中间件已配置 - 支持跨域请求
2025-08-28 14:21:53.998 | INFO     | src.infra.clients.mysql.orm:init_db_engine:64 - MySQL连接成功. 版本: ('8.0.30-txsql',), 连接池配置: pool_size=50, max_overflow=100
/Users/<USER>/Workspace/click-pilot/src/infra/model/__init__.py:17: UserWarning: WARNING! max_completion_tokens is not default parameter.
                max_completion_tokens was transferred to model_kwargs.
                Please confirm that max_completion_tokens is what you intended.
  return init_chat_model(
/Users/<USER>/Workspace/click-pilot/src/infra/model/__init__.py:17: UserWarning: WARNING! max_completion_tokens is not default parameter.
                max_completion_tokens was transferred to model_kwargs.
                Please confirm that max_completion_tokens is what you intended.
  return init_chat_model(
2025-08-28 14:21:55.201 | INFO     | src.scheduler.video_cleanup:<module>:43 - 📅 Video cleanup scheduled task registered: daily at 2:00 AM
2025-08-28 14:21:55.202 | INFO     | src.scheduler.screenshot_cleanup:<module>:43 - 📅 Screenshot cleanup scheduled task registered: daily at 3:00 AM
INFO:     Started server process [25677]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8888 (Press CTRL+C to quit)
2025-08-28 14:21:55.213 | INFO     | src.infra.events.event_bus:start:161 - Starting EventBus...
2025-08-28 14:21:55.225 | INFO     | src.infra.events.event_bus:start:163 - EventBus started successfully
2025-08-28 14:22:03.301 | INFO     | src.infra.scheduler:ok:89 - Scheduler is ready for work...
INFO:     127.0.0.1:61114 - "GET /api/v1/ui-task/record/get?task_id=94dd9d0ad5274eb68c4bf1b6c1e32bb0 HTTP/1.1" 200
2025-08-28 14:23:12.576 | INFO     | src.domain.ui_task.mobile.repo.ui_task_repository:create_task:40 - [f73fc0f9278e4867a85667cbd7c20fea] 🔍 Task created with status: 'processing' (type: <class 'str'>)
2025-08-28 14:23:12.687 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:create_task_from_request:75 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Task created in database: f73fc0f9278e4867a85667cbd7c20fea
2025-08-28 14:23:12.687 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:create_task_from_request:78 - [f73fc0f9278e4867a85667cbd7c20fea] 🔍 Task creation status debug: status='processing' (type: <class 'str'>)
2025-08-28 14:23:12.688 | INFO     | src.application.reference_task_application:create_reference_task_from_request:75 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Reference task record created
2025-08-28 14:23:12.688 | INFO     | src.domain.ui_task.mobile.repo.do.task_stop_manager:register_task:84 - [f73fc0f9278e4867a85667cbd7c20fea] 📝 Task f73fc0f9278e4867a85667cbd7c20fea registered: 热门玩法筛选功能
2025-08-28 14:23:13.205 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell echo 'test'
2025-08-28 14:23:13.256 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:is_device_available:165 - ✅ Device 4f7d025f is responsive
2025-08-28 14:23:13.256 | DEBUG    | src.domain.ui_task.mobile.android.adb_connection_manager:is_device_available:180 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Device 4f7d025f is responsive
2025-08-28 14:23:13.256 | INFO     | src.domain.ui_task.mobile.android.adb_connection_manager:connect_device:89 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Device 4f7d025f is ready for use
2025-08-28 14:23:13.874 | INFO     | src.api.v1.reference_task:execute_reference_task:170 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Device connection successful
2025-08-28 14:23:13.874 | INFO     | src.api.v1.reference_task:execute_reference_task:216 - [f73fc0f9278e4867a85667cbd7c20fea] 🚀 Starting reference task execution: 热门玩法筛选功能 (Task ID: f73fc0f9278e4867a85667cbd7c20fea)
2025-08-28 14:23:13.875 | INFO     | src.api.v1.reference_task:execute_reference_task_background:46 - [f73fc0f9278e4867a85667cbd7c20fea] 🚀 Background execution started for reference task: f73fc0f9278e4867a85667cbd7c20fea
INFO:     127.0.0.1:61339 - "POST /api/v1/reference-task/execute HTTP/1.1" 200
2025-08-28 14:23:14.165 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:start_task_execution:110 - [f73fc0f9278e4867a85667cbd7c20fea] 🚀 Task execution started: f73fc0f9278e4867a85667cbd7c20fea
2025-08-28 14:23:14.165 | INFO     | src.domain.ui_task.mobile.service.keyboard_service:setup_adb_keyboards_for_task:45 - [f73fc0f9278e4867a85667cbd7c20fea] 🎹 Setting up ADB keyboards for device: 4f7d025f
2025-08-28 14:23:14.733 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:167 - [device: 4f7d025f] 🎹 Setting up ADB keyboards...
2025-08-28 14:23:14.733 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:54 - [device: 4f7d025f] Executing shell: settings get secure default_input_method
2025-08-28 14:23:14.733 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell settings get secure default_input_method
2025-08-28 14:23:14.786 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:58 - [device: 4f7d025f] Command success: com.android.adbkeyboard/.AdbIME
2025-08-28 14:23:14.786 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:171 - [device: 4f7d025f] Original IME: com.android.adbkeyboard/.AdbIME
2025-08-28 14:23:14.786 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:181 - [device: 4f7d025f] 🔄 Trying keyboard FastInputIME (priority: 1)...
2025-08-28 14:23:14.786 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:54 - [device: 4f7d025f] Executing shell: ime enable com.github.uiautomator/.FastInputIME
2025-08-28 14:23:14.786 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell ime enable com.github.uiautomator/.FastInputIME
2025-08-28 14:23:14.832 | ERROR    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:68 - ADB command failed: Unknown input method com.github.uiautomator/.FastInputIME cannot be enabled for user #0
2025-08-28 14:23:14.832 | ERROR    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:61 - [device: 4f7d025f] Command error: Unknown input method com.github.uiautomator/.FastInputIME cannot be enabled for user #0
2025-08-28 14:23:14.832 | WARNING  | src.domain.ui_task.mobile.android.keyboard_manager:enable_keyboard:116 - [device: 4f7d025f] ❌ Failed to enable keyboard: com.github.uiautomator/.FastInputIME
2025-08-28 14:23:14.832 | WARNING  | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:186 - [device: 4f7d025f] ❌ Failed to enable FastInputIME, trying next...
2025-08-28 14:23:14.832 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:181 - [device: 4f7d025f] 🔄 Trying keyboard AdbIME (priority: 2)...
2025-08-28 14:23:14.832 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:54 - [device: 4f7d025f] Executing shell: ime enable com.android.adbkeyboard/.AdbIME
2025-08-28 14:23:14.832 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell ime enable com.android.adbkeyboard/.AdbIME
2025-08-28 14:23:14.871 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:58 - [device: 4f7d025f] Command success: Input method com.android.adbkeyboard/.AdbIME: already enabled for user #0
2025-08-28 14:23:14.871 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:enable_keyboard:114 - [device: 4f7d025f] ✅ Enabled keyboard: com.android.adbkeyboard/.AdbIME
2025-08-28 14:23:14.871 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:54 - [device: 4f7d025f] Executing shell: ime list -s
2025-08-28 14:23:14.871 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell ime list -s
2025-08-28 14:23:14.913 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:58 - [device: 4f7d025f] Command success: com.baidu.input_mi/.ImeService
com.iflytek.inputmethod.miui/.FlyIME
com.sohu.inputmethod.sogou.xiaomi/.SogouIME
com.android.adbkeyboard/.AdbIME
2025-08-28 14:23:14.913 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:get_enabled_imes:97 - [device: 4f7d025f] Found enabled IME: com.baidu.input_mi/.ImeService
2025-08-28 14:23:14.914 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:get_enabled_imes:97 - [device: 4f7d025f] Found enabled IME: com.iflytek.inputmethod.miui/.FlyIME
2025-08-28 14:23:14.914 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:get_enabled_imes:97 - [device: 4f7d025f] Found enabled IME: com.sohu.inputmethod.sogou.xiaomi/.SogouIME
2025-08-28 14:23:14.914 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:get_enabled_imes:97 - [device: 4f7d025f] Found enabled IME: com.android.adbkeyboard/.AdbIME
2025-08-28 14:23:14.914 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:get_enabled_imes:98 - [device: 4f7d025f] Total enabled IMEs found: 4
2025-08-28 14:23:14.914 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:195 - [device: 4f7d025f] ✅ AdbIME enabled successfully
2025-08-28 14:23:14.914 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:54 - [device: 4f7d025f] Executing shell: ime set com.android.adbkeyboard/.AdbIME
2025-08-28 14:23:14.914 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell ime set com.android.adbkeyboard/.AdbIME
2025-08-28 14:23:14.953 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:58 - [device: 4f7d025f] Command success: Input method com.android.adbkeyboard/.AdbIME selected for user #0
2025-08-28 14:23:14.953 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:set_keyboard:148 - [device: 4f7d025f] ✅ Set keyboard: com.android.adbkeyboard/.AdbIME
2025-08-28 14:23:14.953 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:54 - [device: 4f7d025f] Executing shell: settings get secure default_input_method
2025-08-28 14:23:14.953 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell settings get secure default_input_method
2025-08-28 14:23:14.998 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:58 - [device: 4f7d025f] Command success: com.android.adbkeyboard/.AdbIME
2025-08-28 14:23:14.998 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:206 - [device: 4f7d025f] 🎉 Successfully set up ADB keyboard: AdbIME
2025-08-28 14:23:14.998 | INFO     | src.domain.ui_task.mobile.service.keyboard_service:setup_adb_keyboards_for_task:61 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ ADB keyboards setup successful
2025-08-28 14:23:15.511 | INFO     | src.api.v1.reference_task:execute_reference_task_background:57 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ ADB keyboards setup successful
2025-08-28 14:23:15.511 | INFO     | src.application.reference_task_application:execute_reference_task:37 - [f73fc0f9278e4867a85667cbd7c20fea] 🚀 Starting reference task execution...
2025-08-28 14:23:15.511 | INFO     | src.application.reference_task_application:execute_reference_task:38 - [f73fc0f9278e4867a85667cbd7c20fea] 📋 Task: 热门玩法筛选功能
2025-08-28 14:23:15.511 | INFO     | src.application.reference_task_application:execute_reference_task:39 - [f73fc0f9278e4867a85667cbd7c20fea] 🔗 Reference task ID: fd07c4dc50624f1694d1b89f974f02cd
2025-08-28 14:23:15.511 | INFO     | src.domain.reference_task.service.reference_task_service:execute_with_reference:48 - [f73fc0f9278e4867a85667cbd7c20fea] 🚀 Starting reference task execution...
2025-08-28 14:23:15.992 | INFO     | src.domain.reference_task.service.reference_task_service:_get_reference_task_actions:132 - Successfully retrieved 9 reference actions
2025-08-28 14:23:15.993 | INFO     | src.domain.reference_task.service.reference_task_service:execute_with_reference:59 - [f73fc0f9278e4867a85667cbd7c20fea] 📋 Found 9 reference actions
2025-08-28 14:23:15.993 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:163 - Parsed 9 steps from task description
2025-08-28 14:23:15.993 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 1: 1.点击页面上方'首页'分类
2025-08-28 14:23:15.993 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 2: 2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
2025-08-28 14:23:15.993 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 3: 3.选中'王者'
2025-08-28 14:23:15.994 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 4: 4.点击【王者】下方的右侧的带图标'筛选'按钮
2025-08-28 14:23:15.994 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 5: 5.选中'热门玩法'的'找女生'选项
2025-08-28 14:23:15.994 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 6: 6.向上拖动寻找'分路'分类
2025-08-28 14:23:15.994 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 7: 7.在'分路'下方右侧点击'展开'按钮，展开更多信息
2025-08-28 14:23:15.994 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 8: 8.向上滑动寻找'打野'，选中'打野'
2025-08-28 14:23:15.994 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 9: 9.点击确定
2025-08-28 14:23:16.127 | INFO     | src.domain.ui_task.mobile.repo.ui_task_repository:update_task_status:91 - [f73fc0f9278e4867a85667cbd7c20fea] 🔄 Status change: processing -> ExecutionStatus.PROCESSING
2025-08-28 14:23:16.302 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:update_task_status:170 - [f73fc0f9278e4867a85667cbd7c20fea] 📝 Task status updated: f73fc0f9278e4867a85667cbd7c20fea -> processing
2025-08-28 14:23:16.533 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:update_task_status:174 - [f73fc0f9278e4867a85667cbd7c20fea] 🔍 Status update verification: status='processing' (type: <class 'str'>)
2025-08-28 14:23:16.539 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:327 - [f73fc0f9278e4867a85667cbd7c20fea] 🎯 Executing reference-based step...
2025-08-28 14:23:16.540 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:346 - [f73fc0f9278e4867a85667cbd7c20fea] 📋 Found 9 steps, current step: 1
2025-08-28 14:23:16.540 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:357 - [f73fc0f9278e4867a85667cbd7c20fea] 🎯 Executing step 1/9: 1.点击页面上方'首页'分类
2025-08-28 14:23:16.540 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_supervisor_check:424 - [f73fc0f9278e4867a85667cbd7c20fea] 🔍 Executing supervisor check...
2025-08-28 14:23:16.540 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:51 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] 🔍 First execution, performing complete checks
2025-08-28 14:23:16.540 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell echo 'test'
2025-08-28 14:23:16.579 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:is_device_available:165 - ✅ Device 4f7d025f is responsive
2025-08-28 14:23:16.579 | DEBUG    | src.domain.ui_task.mobile.android.adb_connection_manager:is_device_available:180 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Device 4f7d025f is responsive
2025-08-28 14:23:16.580 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell pm list packages
2025-08-28 14:23:16.632 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:125 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] ℹ️ First execution with restart disabled, skipping app restart
2025-08-28 14:23:16.633 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell dumpsys window | grep mCurrentFocus
2025-08-28 14:23:16.681 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:138 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] ✓ App com.yiyou.ga is in foreground
2025-08-28 14:23:16.682 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:_check_execution_limits:426 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] 📊 Supervisor check: 0/27 actions executed
2025-08-28 14:23:16.682 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_supervisor_check:436 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Supervisor check passed
2025-08-28 14:23:16.682 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:89 - [f73fc0f9278e4867a85667cbd7c20fea] 🔄 Step attempt 1/8
2025-08-28 14:23:16.682 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:101 - [f73fc0f9278e4867a85667cbd7c20fea] 🔄 Step attempt 1/7
2025-08-28 14:23:17.958 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:create_task_action:326 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Task action created: f73fc0f9278e4867a85667cbd7c20fea - 1.点击页面上方'首页'分类
2025-08-28 14:23:17.958 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:107 - [f73fc0f9278e4867a85667cbd7c20fea] 🧠 Reference decision-execution agent analyzing step: 1.点击页面上方'首页'分类
2025-08-28 14:23:20.963 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:111 - [f73fc0f9278e4867a85667cbd7c20fea] 📸 Taking fresh screenshot before decision...
2025-08-28 14:23:20.965 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell screencap -p /sdcard/temp_screenshot_142320.png
2025-08-28 14:23:21.730 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f pull /sdcard/temp_screenshot_142320.png reports/ui_agent_test/reports/20250828/f73fc0f9278e4867a85667cbd7c20fea/screenshot_1_20250828_142320_965.png
2025-08-28 14:23:21.801 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell rm /sdcard/temp_screenshot_142320.png
2025-08-28 14:23:21.854 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:take_screenshot:278 - 📸 Screenshot saved: reports/ui_agent_test/reports/20250828/f73fc0f9278e4867a85667cbd7c20fea/screenshot_1_20250828_142320_965.png
2025-08-28 14:23:21.855 | INFO     | src.domain.ui_task.mobile.android.screenshot_manager:take_screenshot:254 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] 📸 Screenshot saved: 20250828/f73fc0f9278e4867a85667cbd7c20fea/screenshot_1_20250828_142320_965.png
2025-08-28 14:23:22.327 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_step_reference_screenshots:456 - Loaded 1 step reference images for step '1.点击页面上方'首页'分类' in task fd07c4dc50624f1694d1b89f974f02cd
2025-08-28 14:23:22.327 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_next_step_first_screenshot:485 - Getting next step first screenshot and info for: 2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
2025-08-28 14:23:22.889 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_next_step_first_screenshot:500 - Successfully loaded next step info for: 2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
2025-08-28 14:23:35.705 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:129 - [f73fc0f9278e4867a85667cbd7c20fea] 🧠 Reference model response time: 12.81s
2025-08-28 14:23:35.705 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:130 - [f73fc0f9278e4867a85667cbd7c20fea] 🧠 Reference model response:
{
"interface_analysis": "页面顶部左上角有'首页'和'娱乐'分类标签，'娱乐'高亮，'首页'位于其左侧顶部区域且可点击。",
"action_decision": "结合当前界面与成功案例第1轮分析，需点击'首页'标签切换分类，该元素位于'娱乐'左侧顶部区域，符合点击条件，因此执行点击操作。",
"current_step_name": "点击页面上方'首页'分类",
"action": "click(point=<point>72 73</point>)"
}
2025-08-28 14:23:35.705 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_parse_json_response:338 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Direct JSON parsing successful
2025-08-28 14:23:35.997 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_update_action_record_content:706 - Updated reference action record content for action 6601: 界面分析: 页面顶部左上角有'首页'和'娱乐'分类标签，'娱乐'高亮，'首页'位于其左侧顶部区域且可点击。
决策内容: 结合当前界面与成功案例第1轮分析，需点击'首页'标签切换分类，该元素位于'娱乐'...
2025-08-28 14:23:36.517 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_log_decision_immediately:590 - [f73fc0f9278e4867a85667cbd7c20fea] 📝 Reference decision logged immediately
2025-08-28 14:23:36.517 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_execute_action_command:524 - [f73fc0f9278e4867a85667cbd7c20fea] 🎯 Executing action command: click(point=<point>72 73</point>)
2025-08-28 14:23:36.517 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_execute_action_command:564 - [f73fc0f9278e4867a85667cbd7c20fea] ⚡ Executing action with action_tool: click(point=<point>72 73</point>)
2025-08-28 14:23:36.518 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell wm size
2025-08-28 14:23:36.574 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:get_screen_size:233 - 📱 Screen size detected: 1200x2670
2025-08-28 14:23:36.574 | DEBUG    | src.domain.ui_task.mobile.android.action_tool:_normalize_coordinates:101 - 🎯 坐标归一化: (72, 73) -> (86, 194) (屏幕尺寸: 1200x2670)
2025-08-28 14:23:36.575 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell input tap 86 194
2025-08-28 14:23:36.622 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_execute_action_command:567 - [f73fc0f9278e4867a85667cbd7c20fea] 🎯 Action execution result: {'status': 'success', 'action': 'click', 'device': '4f7d025f', 'message': 'Action executed successfully', 'clicked_element': {'x': 86, 'y': 194}}
2025-08-28 14:23:36.622 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell wm size
2025-08-28 14:23:36.668 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:get_screen_size:233 - 📱 Screen size detected: 1200x2670
2025-08-28 14:23:36.668 | DEBUG    | src.domain.ui_task.mobile.android.action_tool:_normalize_coordinates:101 - 🎯 坐标归一化: (72, 73) -> (86, 194) (屏幕尺寸: 1200x2670)
2025-08-28 14:23:36.668 | INFO     | src.domain.ui_task.mobile.android.image_processor:parse_coordinates_and_direction_from_action:209 - 📍 Parsed coordinates from 'click(point=<point>72 73</point>)': (86, 194)
2025-08-28 14:23:36.668 | INFO     | src.domain.ui_task.mobile.android.image_processor:annotate_screenshot_from_action:346 - 📍 Parsed coordinates from 'click(point=<point>72 73</point>)': (86, 194), direction=None
2025-08-28 14:23:36.742 | INFO     | src.domain.ui_task.mobile.android.image_processor:_draw_enhanced_marker:107 - 📍 Enhanced marker drawn at (86, 194) with outer_radius=57, direction=None
2025-08-28 14:23:37.121 | INFO     | src.domain.ui_task.mobile.android.image_processor:annotate_screenshot_with_coordinates:314 - 📸 Screenshot annotated in place: 20250828/f73fc0f9278e4867a85667cbd7c20fea/screenshot_1_20250828_142320_965.png, direction=None
2025-08-28 14:23:37.121 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_annotate_screenshot_after_execution:744 - [f73fc0f9278e4867a85667cbd7c20fea] 🎯 Screenshot annotated after reference execution: click(point=<point>72 73</point>)
2025-08-28 14:23:37.710 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:complete_task_action:369 - [action_id: 6601] ✅ Task action success: 6601
2025-08-28 14:23:37.711 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_complete_step_action_record:653 - Reference step action record completed: 成功 - 动作执行成功
2025-08-28 14:23:37.711 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:181 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Execution result recorded to history
2025-08-28 14:23:37.714 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:327 - [f73fc0f9278e4867a85667cbd7c20fea] 🎯 Executing reference-based step...
2025-08-28 14:23:37.714 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:346 - [f73fc0f9278e4867a85667cbd7c20fea] 📋 Found 9 steps, current step: 1
2025-08-28 14:23:37.714 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:357 - [f73fc0f9278e4867a85667cbd7c20fea] 🎯 Executing step 1/9: 1.点击页面上方'首页'分类
2025-08-28 14:23:37.714 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_supervisor_check:424 - [f73fc0f9278e4867a85667cbd7c20fea] 🔍 Executing supervisor check...
2025-08-28 14:23:37.714 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:51 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] 🔍 First execution, performing complete checks
2025-08-28 14:23:37.714 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell echo 'test'
2025-08-28 14:23:37.755 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:is_device_available:165 - ✅ Device 4f7d025f is responsive
2025-08-28 14:23:37.755 | DEBUG    | src.domain.ui_task.mobile.android.adb_connection_manager:is_device_available:180 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Device 4f7d025f is responsive
2025-08-28 14:23:37.755 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell pm list packages
2025-08-28 14:23:37.810 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:125 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] ℹ️ First execution with restart disabled, skipping app restart
2025-08-28 14:23:37.810 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell dumpsys window | grep mCurrentFocus
2025-08-28 14:23:37.868 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:138 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] ✓ App com.yiyou.ga is in foreground
2025-08-28 14:23:37.868 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:_check_execution_limits:426 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] 📊 Supervisor check: 0/27 actions executed
2025-08-28 14:23:37.868 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_supervisor_check:436 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Supervisor check passed
2025-08-28 14:23:37.868 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:89 - [f73fc0f9278e4867a85667cbd7c20fea] 🔄 Step attempt 2/8
2025-08-28 14:23:37.868 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:101 - [f73fc0f9278e4867a85667cbd7c20fea] 🔄 Step attempt 2/7
2025-08-28 14:23:39.155 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:create_task_action:326 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Task action created: f73fc0f9278e4867a85667cbd7c20fea - 1.点击页面上方'首页'分类
2025-08-28 14:23:39.155 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:107 - [f73fc0f9278e4867a85667cbd7c20fea] 🧠 Reference decision-execution agent analyzing step: 1.点击页面上方'首页'分类
2025-08-28 14:23:42.159 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:111 - [f73fc0f9278e4867a85667cbd7c20fea] 📸 Taking fresh screenshot before decision...
2025-08-28 14:23:42.160 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell screencap -p /sdcard/temp_screenshot_142342.png
2025-08-28 14:23:42.846 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f pull /sdcard/temp_screenshot_142342.png reports/ui_agent_test/reports/20250828/f73fc0f9278e4867a85667cbd7c20fea/screenshot_1_20250828_142342_160.png
2025-08-28 14:23:42.910 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell rm /sdcard/temp_screenshot_142342.png
2025-08-28 14:23:42.957 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:take_screenshot:278 - 📸 Screenshot saved: reports/ui_agent_test/reports/20250828/f73fc0f9278e4867a85667cbd7c20fea/screenshot_1_20250828_142342_160.png
2025-08-28 14:23:42.958 | INFO     | src.domain.ui_task.mobile.android.screenshot_manager:take_screenshot:254 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] 📸 Screenshot saved: 20250828/f73fc0f9278e4867a85667cbd7c20fea/screenshot_1_20250828_142342_160.png
2025-08-28 14:23:43.442 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_step_reference_screenshots:456 - Loaded 1 step reference images for step '1.点击页面上方'首页'分类' in task fd07c4dc50624f1694d1b89f974f02cd
2025-08-28 14:23:43.442 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_next_step_first_screenshot:485 - Getting next step first screenshot and info for: 2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
2025-08-28 14:23:43.917 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_next_step_first_screenshot:500 - Successfully loaded next step info for: 2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
INFO:     127.0.0.1:61571 - "GET /api/v1/ui-task/record/get?task_id=f73fc0f9278e4867a85667cbd7c20fea HTTP/1.1" 200
INFO:     127.0.0.1:61576 - "GET /api/v1/ui-task/record/get?task_id=f73fc0f9278e4867a85667cbd7c20fea HTTP/1.1" 200
2025-08-28 14:24:21.587 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:129 - [f73fc0f9278e4867a85667cbd7c20fea] 🧠 Reference model response time: 37.67s
2025-08-28 14:24:21.588 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:130 - [f73fc0f9278e4867a85667cbd7c20fea] 🧠 Reference model response:
{
"interface_analysis": "当前界面顶部“首页”高亮，下方功能模块区域包含“游戏搭子”“心动速配”“扩列派对”“热门玩法”等，功能模块下方的二级导航栏横向排列，显示“推荐”“桌球”“和平”“第五”“蛋仔”“金铲铲”“接歌”“更多”等标签，无“王者”标签，需横向滑动导航栏查找。",
"action_decision": "当前步骤为“在二级导航栏中滑动找到'王者'标签”，结合成功案例第2轮的决策，二级导航栏当前可见标签无“王者”，需从“第五”标签位置向左滑动导航栏以露出右侧隐藏标签，因此执行滑动操作。",
"current_step_name": "在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方",
"action": "scroll(point='<point>436 528</point>', direction='left')"
}
2025-08-28 14:24:21.588 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_parse_json_response:338 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Direct JSON parsing successful
2025-08-28 14:24:21.878 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_update_action_record_content:706 - Updated reference action record content for action 6602: 界面分析: 当前界面顶部“首页”高亮，下方功能模块区域包含“游戏搭子”“心动速配”“扩列派对”“热门玩法”等，功能模块下方的二级导航栏横向排列，显示“推荐”“桌球”“和平”“第五”“蛋仔”“金铲铲”“...
2025-08-28 14:24:22.394 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_log_decision_immediately:590 - [f73fc0f9278e4867a85667cbd7c20fea] 📝 Reference decision logged immediately
2025-08-28 14:24:22.394 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_check_and_switch_step:910 - [f73fc0f9278e4867a85667cbd7c20fea] 🔄 Step switching from index 0 to 1
2025-08-28 14:24:22.394 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_check_and_switch_step:911 - [f73fc0f9278e4867a85667cbd7c20fea] 🔄 Step switching from '1.点击页面上方'首页'分类' to '2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方'
2025-08-28 14:24:22.394 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:147 - [f73fc0f9278e4867a85667cbd7c20fea] 🔄 Step switched to: 在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
2025-08-28 14:24:22.394 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_execute_action_command:524 - [f73fc0f9278e4867a85667cbd7c20fea] 🎯 Executing action command: scroll(point='<point>436 528</point>', direction='left')
2025-08-28 14:24:22.395 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_execute_action_command:564 - [f73fc0f9278e4867a85667cbd7c20fea] ⚡ Executing action with action_tool: scroll(point='<point>436 528</point>', direction='left')
2025-08-28 14:24:22.395 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell wm size
2025-08-28 14:24:22.469 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:get_screen_size:233 - 📱 Screen size detected: 1200x2670
2025-08-28 14:24:22.470 | DEBUG    | src.domain.ui_task.mobile.android.action_tool:_normalize_coordinates:101 - 🎯 坐标归一化: (436, 528) -> (523, 1409) (屏幕尺寸: 1200x2670)
2025-08-28 14:24:22.470 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell wm size
2025-08-28 14:24:22.528 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:get_screen_size:233 - 📱 Screen size detected: 1200x2670
2025-08-28 14:24:22.528 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell wm size
2025-08-28 14:24:22.589 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:get_screen_size:233 - 📱 Screen size detected: 1200x2670
2025-08-28 14:24:22.590 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell input swipe 523 1409 123.0 1409 500
2025-08-28 14:24:23.150 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_execute_action_command:567 - [f73fc0f9278e4867a85667cbd7c20fea] 🎯 Action execution result: {'status': 'success', 'action': 'scroll', 'device': '4f7d025f', 'message': 'Action executed successfully', 'scroll': {'start': {'x': 523, 'y': 1409}, 'end': {'x': 123.0, 'y': 1409}, 'direction': 'left'}}
2025-08-28 14:24:23.151 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell wm size
2025-08-28 14:24:23.209 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:get_screen_size:233 - 📱 Screen size detected: 1200x2670
2025-08-28 14:24:23.210 | DEBUG    | src.domain.ui_task.mobile.android.action_tool:_normalize_coordinates:101 - 🎯 坐标归一化: (436, 528) -> (523, 1409) (屏幕尺寸: 1200x2670)
2025-08-28 14:24:23.210 | INFO     | src.domain.ui_task.mobile.android.image_processor:parse_coordinates_and_direction_from_action:243 - 📍 Parsed scroll coordinates from 'scroll(point='<point>436 528</point>', direction='left')': (523, 1409), direction=left
2025-08-28 14:24:23.210 | INFO     | src.domain.ui_task.mobile.android.image_processor:annotate_screenshot_from_action:346 - 📍 Parsed coordinates from 'scroll(point='<point>436 528</point>', direction='left')': (523, 1409), direction=left
2025-08-28 14:24:23.210 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell wm size
2025-08-28 14:24:23.262 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:get_screen_size:233 - 📱 Screen size detected: 1200x2670
25 5 15
2025-08-28 14:24:23.328 | INFO     | src.domain.ui_task.mobile.android.image_processor:_draw_direction_arrow:177 - 🏹 Small direction arrow drawn: left from (480, 1409) to (455, 1409)
2025-08-28 14:24:23.328 | INFO     | src.domain.ui_task.mobile.android.image_processor:_draw_enhanced_marker:107 - 📍 Enhanced marker drawn at (523, 1409) with outer_radius=57, direction=left
2025-08-28 14:24:23.690 | INFO     | src.domain.ui_task.mobile.android.image_processor:annotate_screenshot_with_coordinates:314 - 📸 Screenshot annotated in place: 20250828/f73fc0f9278e4867a85667cbd7c20fea/screenshot_1_20250828_142342_160.png, direction=left
2025-08-28 14:24:23.690 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_annotate_screenshot_after_execution:744 - [f73fc0f9278e4867a85667cbd7c20fea] 🎯 Screenshot annotated after reference execution: scroll(point='<point>436 528</point>', direction='left')
2025-08-28 14:24:24.333 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:complete_task_action:369 - [action_id: 6602] ✅ Task action success: 6602
2025-08-28 14:24:24.333 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_complete_step_action_record:653 - Reference step action record completed: 成功 - 动作执行成功
2025-08-28 14:24:24.334 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:181 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Execution result recorded to history
2025-08-28 14:24:24.337 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:327 - [f73fc0f9278e4867a85667cbd7c20fea] 🎯 Executing reference-based step...
2025-08-28 14:24:24.337 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:346 - [f73fc0f9278e4867a85667cbd7c20fea] 📋 Found 9 steps, current step: 2
2025-08-28 14:24:24.337 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:357 - [f73fc0f9278e4867a85667cbd7c20fea] 🎯 Executing step 2/9: 2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
2025-08-28 14:24:24.337 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_supervisor_check:424 - [f73fc0f9278e4867a85667cbd7c20fea] 🔍 Executing supervisor check...
2025-08-28 14:24:24.337 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:51 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] 🔍 First execution, performing complete checks
2025-08-28 14:24:24.338 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell echo 'test'
2025-08-28 14:24:24.397 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:is_device_available:165 - ✅ Device 4f7d025f is responsive
2025-08-28 14:24:24.397 | DEBUG    | src.domain.ui_task.mobile.android.adb_connection_manager:is_device_available:180 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Device 4f7d025f is responsive
2025-08-28 14:24:24.397 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell pm list packages
2025-08-28 14:24:24.446 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:125 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] ℹ️ First execution with restart disabled, skipping app restart
2025-08-28 14:24:24.446 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell dumpsys window | grep mCurrentFocus
2025-08-28 14:24:24.502 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:138 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] ✓ App com.yiyou.ga is in foreground
2025-08-28 14:24:24.502 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:_check_execution_limits:426 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] 📊 Supervisor check: 0/27 actions executed
2025-08-28 14:24:24.502 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_supervisor_check:436 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Supervisor check passed
2025-08-28 14:24:24.502 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:89 - [f73fc0f9278e4867a85667cbd7c20fea] 🔄 Step attempt 2/8
2025-08-28 14:24:24.502 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:101 - [f73fc0f9278e4867a85667cbd7c20fea] 🔄 Step attempt 2/7
INFO:     127.0.0.1:61739 - "GET /api/v1/ui-task/record/get?task_id=f73fc0f9278e4867a85667cbd7c20fea HTTP/1.1" 200
2025-08-28 14:24:25.785 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:create_task_action:326 - [f73fc0f9278e4867a85667cbd7c20fea] ✅ Task action created: f73fc0f9278e4867a85667cbd7c20fea - 2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
2025-08-28 14:24:25.785 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:107 - [f73fc0f9278e4867a85667cbd7c20fea] 🧠 Reference decision-execution agent analyzing step: 2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
2025-08-28 14:24:28.790 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:111 - [f73fc0f9278e4867a85667cbd7c20fea] 📸 Taking fresh screenshot before decision...
2025-08-28 14:24:28.791 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell screencap -p /sdcard/temp_screenshot_142428.png
2025-08-28 14:24:29.460 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f pull /sdcard/temp_screenshot_142428.png reports/ui_agent_test/reports/20250828/f73fc0f9278e4867a85667cbd7c20fea/screenshot_1_20250828_142428_791.png
2025-08-28 14:24:29.524 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell rm /sdcard/temp_screenshot_142428.png
2025-08-28 14:24:29.569 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:take_screenshot:278 - 📸 Screenshot saved: reports/ui_agent_test/reports/20250828/f73fc0f9278e4867a85667cbd7c20fea/screenshot_1_20250828_142428_791.png
2025-08-28 14:24:29.570 | INFO     | src.domain.ui_task.mobile.android.screenshot_manager:take_screenshot:254 - [task_id: f73fc0f9278e4867a85667cbd7c20fea] 📸 Screenshot saved: 20250828/f73fc0f9278e4867a85667cbd7c20fea/screenshot_1_20250828_142428_791.png
2025-08-28 14:24:30.062 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_step_reference_screenshots:456 - Loaded 1 step reference images for step '2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方' in task fd07c4dc50624f1694d1b89f974f02cd
2025-08-28 14:24:30.063 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_next_step_first_screenshot:485 - Getting next step first screenshot and info for: 3.选中'王者'
2025-08-28 14:24:30.655 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_next_step_first_screenshot:500 - Successfully loaded next step info for: 3.选中'王者'
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-08-28 14:24:33.112 | INFO     | src.infra.app:_on_stop_with_thread:76 - Waiting 3 seconds for the cleanup thread...
2025-08-28 14:24:33.113 | INFO     | src.infra.events.event_bus:stop:174 - Stopping EventBus...

Process finished with exit code 137 (interrupted by signal 9:SIGKILL)
