/Users/<USER>/Workspace/click-pilot/.venv/bin/python /Users/<USER>/Workspace/click-pilot/main.py
/Users/<USER>/Workspace/click-pilot/.venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:198: UserWarning: Field name "copy" in "ModelSettings" shadows an attribute in parent "BaseModel"
  warnings.warn(
2025-08-28 14:12:57.015 | INFO     | src.infra.web.http:_setup_cors:56 - ✅ CORS中间件已配置 - 支持跨域请求
2025-08-28 14:12:57.807 | INFO     | src.infra.clients.mysql.orm:init_db_engine:64 - MySQL连接成功. 版本: ('8.0.30-txsql',), 连接池配置: pool_size=50, max_overflow=100
/Users/<USER>/Workspace/click-pilot/src/infra/model/__init__.py:17: UserWarning: WARNING! max_completion_tokens is not default parameter.
                max_completion_tokens was transferred to model_kwargs.
                Please confirm that max_completion_tokens is what you intended.
  return init_chat_model(
/Users/<USER>/Workspace/click-pilot/src/infra/model/__init__.py:17: UserWarning: WARNING! max_completion_tokens is not default parameter.
                max_completion_tokens was transferred to model_kwargs.
                Please confirm that max_completion_tokens is what you intended.
  return init_chat_model(
2025-08-28 14:12:58.551 | INFO     | src.scheduler.video_cleanup:<module>:43 - 📅 Video cleanup scheduled task registered: daily at 2:00 AM
2025-08-28 14:12:58.551 | INFO     | src.scheduler.screenshot_cleanup:<module>:43 - 📅 Screenshot cleanup scheduled task registered: daily at 3:00 AM
2025-08-28 14:12:58.564 | INFO     | src.infra.events.event_bus:start:161 - Starting EventBus...
INFO:     Started server process [23186]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8888 (Press CTRL+C to quit)
2025-08-28 14:12:58.576 | INFO     | src.infra.events.event_bus:start:163 - EventBus started successfully
2025-08-28 14:13:06.394 | INFO     | src.domain.ui_task.mobile.repo.ui_task_repository:create_task:40 - [c914844f201142b3af4e240dc4756b9e] 🔍 Task created with status: 'processing' (type: <class 'str'>)
2025-08-28 14:13:06.506 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:create_task_from_request:75 - [c914844f201142b3af4e240dc4756b9e] ✅ Task created in database: c914844f201142b3af4e240dc4756b9e
2025-08-28 14:13:06.506 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:create_task_from_request:78 - [c914844f201142b3af4e240dc4756b9e] 🔍 Task creation status debug: status='processing' (type: <class 'str'>)
2025-08-28 14:13:06.507 | INFO     | src.application.reference_task_application:create_reference_task_from_request:75 - [c914844f201142b3af4e240dc4756b9e] ✅ Reference task record created
2025-08-28 14:13:06.507 | INFO     | src.domain.ui_task.mobile.repo.do.task_stop_manager:register_task:84 - [c914844f201142b3af4e240dc4756b9e] 📝 Task c914844f201142b3af4e240dc4756b9e registered: 热门玩法筛选功能
2025-08-28 14:13:07.087 | INFO     | src.infra.scheduler:ok:89 - Scheduler is ready for work...
2025-08-28 14:13:07.120 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell echo 'test'
2025-08-28 14:13:07.173 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:is_device_available:165 - ✅ Device 4f7d025f is responsive
2025-08-28 14:13:07.174 | DEBUG    | src.domain.ui_task.mobile.android.adb_connection_manager:is_device_available:180 - [c914844f201142b3af4e240dc4756b9e] ✅ Device 4f7d025f is responsive
2025-08-28 14:13:07.174 | INFO     | src.domain.ui_task.mobile.android.adb_connection_manager:connect_device:89 - [c914844f201142b3af4e240dc4756b9e] ✅ Device 4f7d025f is ready for use
2025-08-28 14:13:07.683 | INFO     | src.api.v1.reference_task:execute_reference_task:170 - [c914844f201142b3af4e240dc4756b9e] ✅ Device connection successful
2025-08-28 14:13:07.683 | INFO     | src.api.v1.reference_task:execute_reference_task:216 - [c914844f201142b3af4e240dc4756b9e] 🚀 Starting reference task execution: 热门玩法筛选功能 (Task ID: c914844f201142b3af4e240dc4756b9e)
2025-08-28 14:13:07.684 | INFO     | src.api.v1.reference_task:execute_reference_task_background:46 - [c914844f201142b3af4e240dc4756b9e] 🚀 Background execution started for reference task: c914844f201142b3af4e240dc4756b9e
INFO:     127.0.0.1:56886 - "POST /api/v1/reference-task/execute HTTP/1.1" 200
2025-08-28 14:13:07.987 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:start_task_execution:110 - [c914844f201142b3af4e240dc4756b9e] 🚀 Task execution started: c914844f201142b3af4e240dc4756b9e
2025-08-28 14:13:07.987 | INFO     | src.domain.ui_task.mobile.service.keyboard_service:setup_adb_keyboards_for_task:45 - [c914844f201142b3af4e240dc4756b9e] 🎹 Setting up ADB keyboards for device: 4f7d025f
2025-08-28 14:13:08.652 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:167 - [device: 4f7d025f] 🎹 Setting up ADB keyboards...
2025-08-28 14:13:08.653 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:54 - [device: 4f7d025f] Executing shell: settings get secure default_input_method
2025-08-28 14:13:08.653 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell settings get secure default_input_method
2025-08-28 14:13:08.702 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:58 - [device: 4f7d025f] Command success: com.android.adbkeyboard/.AdbIME
2025-08-28 14:13:08.702 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:171 - [device: 4f7d025f] Original IME: com.android.adbkeyboard/.AdbIME
2025-08-28 14:13:08.702 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:181 - [device: 4f7d025f] 🔄 Trying keyboard FastInputIME (priority: 1)...
2025-08-28 14:13:08.702 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:54 - [device: 4f7d025f] Executing shell: ime enable com.github.uiautomator/.FastInputIME
2025-08-28 14:13:08.702 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell ime enable com.github.uiautomator/.FastInputIME
2025-08-28 14:13:08.750 | ERROR    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:68 - ADB command failed: Unknown input method com.github.uiautomator/.FastInputIME cannot be enabled for user #0
2025-08-28 14:13:08.751 | ERROR    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:61 - [device: 4f7d025f] Command error: Unknown input method com.github.uiautomator/.FastInputIME cannot be enabled for user #0
2025-08-28 14:13:08.751 | WARNING  | src.domain.ui_task.mobile.android.keyboard_manager:enable_keyboard:116 - [device: 4f7d025f] ❌ Failed to enable keyboard: com.github.uiautomator/.FastInputIME
2025-08-28 14:13:08.751 | WARNING  | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:186 - [device: 4f7d025f] ❌ Failed to enable FastInputIME, trying next...
2025-08-28 14:13:08.751 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:181 - [device: 4f7d025f] 🔄 Trying keyboard AdbIME (priority: 2)...
2025-08-28 14:13:08.751 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:54 - [device: 4f7d025f] Executing shell: ime enable com.android.adbkeyboard/.AdbIME
2025-08-28 14:13:08.751 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell ime enable com.android.adbkeyboard/.AdbIME
2025-08-28 14:13:08.800 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:58 - [device: 4f7d025f] Command success: Input method com.android.adbkeyboard/.AdbIME: already enabled for user #0
2025-08-28 14:13:08.800 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:enable_keyboard:114 - [device: 4f7d025f] ✅ Enabled keyboard: com.android.adbkeyboard/.AdbIME
2025-08-28 14:13:08.800 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:54 - [device: 4f7d025f] Executing shell: ime list -s
2025-08-28 14:13:08.800 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell ime list -s
2025-08-28 14:13:08.847 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:58 - [device: 4f7d025f] Command success: com.baidu.input_mi/.ImeService
com.iflytek.inputmethod.miui/.FlyIME
com.sohu.inputmethod.sogou.xiaomi/.SogouIME
com.android.adbkeyboard/.AdbIME
2025-08-28 14:13:08.848 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:get_enabled_imes:97 - [device: 4f7d025f] Found enabled IME: com.baidu.input_mi/.ImeService
2025-08-28 14:13:08.848 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:get_enabled_imes:97 - [device: 4f7d025f] Found enabled IME: com.iflytek.inputmethod.miui/.FlyIME
2025-08-28 14:13:08.848 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:get_enabled_imes:97 - [device: 4f7d025f] Found enabled IME: com.sohu.inputmethod.sogou.xiaomi/.SogouIME
2025-08-28 14:13:08.848 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:get_enabled_imes:97 - [device: 4f7d025f] Found enabled IME: com.android.adbkeyboard/.AdbIME
2025-08-28 14:13:08.848 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:get_enabled_imes:98 - [device: 4f7d025f] Total enabled IMEs found: 4
2025-08-28 14:13:08.848 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:195 - [device: 4f7d025f] ✅ AdbIME enabled successfully
2025-08-28 14:13:08.848 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:54 - [device: 4f7d025f] Executing shell: ime set com.android.adbkeyboard/.AdbIME
2025-08-28 14:13:08.848 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell ime set com.android.adbkeyboard/.AdbIME
2025-08-28 14:13:08.904 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:58 - [device: 4f7d025f] Command success: Input method com.android.adbkeyboard/.AdbIME selected for user #0
2025-08-28 14:13:08.904 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:set_keyboard:148 - [device: 4f7d025f] ✅ Set keyboard: com.android.adbkeyboard/.AdbIME
2025-08-28 14:13:08.904 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:54 - [device: 4f7d025f] Executing shell: settings get secure default_input_method
2025-08-28 14:13:08.904 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell settings get secure default_input_method
2025-08-28 14:13:08.946 | DEBUG    | src.domain.ui_task.mobile.android.keyboard_manager:_execute_shell_command:58 - [device: 4f7d025f] Command success: com.android.adbkeyboard/.AdbIME
2025-08-28 14:13:08.947 | INFO     | src.domain.ui_task.mobile.android.keyboard_manager:setup_adb_keyboards:206 - [device: 4f7d025f] 🎉 Successfully set up ADB keyboard: AdbIME
2025-08-28 14:13:08.947 | INFO     | src.domain.ui_task.mobile.service.keyboard_service:setup_adb_keyboards_for_task:61 - [c914844f201142b3af4e240dc4756b9e] ✅ ADB keyboards setup successful
2025-08-28 14:13:09.471 | INFO     | src.api.v1.reference_task:execute_reference_task_background:57 - [c914844f201142b3af4e240dc4756b9e] ✅ ADB keyboards setup successful
2025-08-28 14:13:09.471 | INFO     | src.application.reference_task_application:execute_reference_task:37 - [c914844f201142b3af4e240dc4756b9e] 🚀 Starting reference task execution...
2025-08-28 14:13:09.471 | INFO     | src.application.reference_task_application:execute_reference_task:38 - [c914844f201142b3af4e240dc4756b9e] 📋 Task: 热门玩法筛选功能
2025-08-28 14:13:09.471 | INFO     | src.application.reference_task_application:execute_reference_task:39 - [c914844f201142b3af4e240dc4756b9e] 🔗 Reference task ID: fd07c4dc50624f1694d1b89f974f02cd
2025-08-28 14:13:09.471 | INFO     | src.domain.reference_task.service.reference_task_service:execute_with_reference:48 - [c914844f201142b3af4e240dc4756b9e] 🚀 Starting reference task execution...
2025-08-28 14:13:10.032 | INFO     | src.domain.reference_task.service.reference_task_service:_get_reference_task_actions:132 - Successfully retrieved 9 reference actions
2025-08-28 14:13:10.032 | INFO     | src.domain.reference_task.service.reference_task_service:execute_with_reference:59 - [c914844f201142b3af4e240dc4756b9e] 📋 Found 9 reference actions
2025-08-28 14:13:10.033 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:163 - Parsed 9 steps from task description
2025-08-28 14:13:10.033 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 1: 1.点击页面上方'首页'分类
2025-08-28 14:13:10.033 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 2: 2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
2025-08-28 14:13:10.033 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 3: 3.选中'王者'
2025-08-28 14:13:10.033 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 4: 4.点击【王者】下方的右侧的带图标'筛选'按钮
2025-08-28 14:13:10.033 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 5: 5.选中'热门玩法'的'找女生'选项
2025-08-28 14:13:10.033 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 6: 6.向上拖动寻找'分路'分类
2025-08-28 14:13:10.033 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 7: 7.在'分路'下方右侧点击'展开'按钮，展开更多信息
2025-08-28 14:13:10.033 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 8: 8.向上滑动寻找'打野'，选中'打野'
2025-08-28 14:13:10.033 | INFO     | src.domain.reference_task.service.reference_task_service:_parse_task_steps:165 - Step 9: 9.点击确定
2025-08-28 14:13:10.261 | INFO     | src.domain.ui_task.mobile.repo.ui_task_repository:update_task_status:91 - [c914844f201142b3af4e240dc4756b9e] 🔄 Status change: processing -> ExecutionStatus.PROCESSING
2025-08-28 14:13:10.430 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:update_task_status:170 - [c914844f201142b3af4e240dc4756b9e] 📝 Task status updated: c914844f201142b3af4e240dc4756b9e -> processing
2025-08-28 14:13:10.656 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:update_task_status:174 - [c914844f201142b3af4e240dc4756b9e] 🔍 Status update verification: status='processing' (type: <class 'str'>)
2025-08-28 14:13:10.663 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:327 - [c914844f201142b3af4e240dc4756b9e] 🎯 Executing reference-based step...
2025-08-28 14:13:10.663 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:346 - [c914844f201142b3af4e240dc4756b9e] 📋 Found 9 steps, current step: 1
2025-08-28 14:13:10.663 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:357 - [c914844f201142b3af4e240dc4756b9e] 🎯 Executing step 1/9: 1.点击页面上方'首页'分类
2025-08-28 14:13:10.663 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_supervisor_check:424 - [c914844f201142b3af4e240dc4756b9e] 🔍 Executing supervisor check...
2025-08-28 14:13:10.664 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:51 - [task_id: c914844f201142b3af4e240dc4756b9e] 🔍 First execution, performing complete checks
2025-08-28 14:13:10.664 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell echo 'test'
2025-08-28 14:13:10.712 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:is_device_available:165 - ✅ Device 4f7d025f is responsive
2025-08-28 14:13:10.712 | DEBUG    | src.domain.ui_task.mobile.android.adb_connection_manager:is_device_available:180 - [c914844f201142b3af4e240dc4756b9e] ✅ Device 4f7d025f is responsive
2025-08-28 14:13:10.712 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell pm list packages
2025-08-28 14:13:10.771 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:125 - [task_id: c914844f201142b3af4e240dc4756b9e] ℹ️ First execution with restart disabled, skipping app restart
2025-08-28 14:13:10.771 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell dumpsys window | grep mCurrentFocus
2025-08-28 14:13:10.831 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:138 - [task_id: c914844f201142b3af4e240dc4756b9e] ✓ App com.yiyou.ga is in foreground
2025-08-28 14:13:10.831 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:_check_execution_limits:426 - [task_id: c914844f201142b3af4e240dc4756b9e] 📊 Supervisor check: 0/27 actions executed
2025-08-28 14:13:10.831 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_supervisor_check:436 - [c914844f201142b3af4e240dc4756b9e] ✅ Supervisor check passed
2025-08-28 14:13:10.831 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:89 - [c914844f201142b3af4e240dc4756b9e] 🔄 Step attempt 1/8
2025-08-28 14:13:10.831 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:101 - [c914844f201142b3af4e240dc4756b9e] 🔄 Step attempt 1/7
2025-08-28 14:13:11.964 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:create_task_action:326 - [c914844f201142b3af4e240dc4756b9e] ✅ Task action created: c914844f201142b3af4e240dc4756b9e - 1.点击页面上方'首页'分类
2025-08-28 14:13:11.965 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:107 - [c914844f201142b3af4e240dc4756b9e] 🧠 Reference decision-execution agent analyzing step: 1.点击页面上方'首页'分类
2025-08-28 14:13:14.970 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:111 - [c914844f201142b3af4e240dc4756b9e] 📸 Taking fresh screenshot before decision...
2025-08-28 14:13:14.971 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell screencap -p /sdcard/temp_screenshot_141314.png
2025-08-28 14:13:15.560 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f pull /sdcard/temp_screenshot_141314.png reports/ui_agent_test/reports/20250828/c914844f201142b3af4e240dc4756b9e/screenshot_1_20250828_141314_971.png
2025-08-28 14:13:15.628 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell rm /sdcard/temp_screenshot_141314.png
2025-08-28 14:13:15.676 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:take_screenshot:278 - 📸 Screenshot saved: reports/ui_agent_test/reports/20250828/c914844f201142b3af4e240dc4756b9e/screenshot_1_20250828_141314_971.png
2025-08-28 14:13:15.676 | INFO     | src.domain.ui_task.mobile.android.screenshot_manager:take_screenshot:254 - [task_id: c914844f201142b3af4e240dc4756b9e] 📸 Screenshot saved: 20250828/c914844f201142b3af4e240dc4756b9e/screenshot_1_20250828_141314_971.png
2025-08-28 14:13:16.148 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_step_reference_screenshots:456 - Loaded 1 step reference images for step '1.点击页面上方'首页'分类' in task fd07c4dc50624f1694d1b89f974f02cd
2025-08-28 14:13:16.149 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_next_step_first_screenshot:485 - Getting next step first screenshot and info for: 2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
2025-08-28 14:13:16.707 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_next_step_first_screenshot:500 - Successfully loaded next step info for: 2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
2025-08-28 14:13:24.879 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:129 - [c914844f201142b3af4e240dc4756b9e] 🧠 Reference model response time: 8.17s
2025-08-28 14:13:24.879 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:130 - [c914844f201142b3af4e240dc4756b9e] 🧠 Reference model response:
{
"interface_analysis": "页面顶部左上角有'首页'和'娱乐'分类标签，当前'娱乐'高亮，'首页'位于其左边且可点击。",
"action_decision": "当前处于测试用例的第1步，需点击页面上方的'首页'标签以切换分类，该元素文本为'首页'，位于'娱乐'左侧顶部区域，且颜色不同表示未选中状态，符合点击条件，参照成功案例第1轮执行的决策内容和动作，决定点击该标签。",
"current_step_name": "点击页面上方'首页'分类",
"action": "click(point=<point>75 73</point>)"
}
2025-08-28 14:13:24.880 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_parse_json_response:338 - [c914844f201142b3af4e240dc4756b9e] ✅ Direct JSON parsing successful
2025-08-28 14:13:24.880 | ERROR    | src.domain.reference_task.agent.reference_decision_agent:_update_action_record_content:714 - Failed to update reference action record content: No module named 'src.domain.ui_task.mobile.repo.action_repo'
2025-08-28 14:13:25.421 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_log_decision_immediately:590 - [c914844f201142b3af4e240dc4756b9e] 📝 Reference decision logged immediately
2025-08-28 14:13:25.421 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_execute_action_command:524 - [c914844f201142b3af4e240dc4756b9e] 🎯 Executing action command: click(point=<point>75 73</point>)
2025-08-28 14:13:25.421 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_execute_action_command:564 - [c914844f201142b3af4e240dc4756b9e] ⚡ Executing action with action_tool: click(point=<point>75 73</point>)
2025-08-28 14:13:25.422 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell wm size
2025-08-28 14:13:25.486 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:get_screen_size:233 - 📱 Screen size detected: 1200x2670
2025-08-28 14:13:25.486 | DEBUG    | src.domain.ui_task.mobile.android.action_tool:_normalize_coordinates:101 - 🎯 坐标归一化: (75, 73) -> (90, 194) (屏幕尺寸: 1200x2670)
2025-08-28 14:13:25.486 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell input tap 90 194
2025-08-28 14:13:25.534 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_execute_action_command:567 - [c914844f201142b3af4e240dc4756b9e] 🎯 Action execution result: {'status': 'success', 'action': 'click', 'device': '4f7d025f', 'message': 'Action executed successfully', 'clicked_element': {'x': 90, 'y': 194}}
2025-08-28 14:13:25.535 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell wm size
2025-08-28 14:13:25.578 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:get_screen_size:233 - 📱 Screen size detected: 1200x2670
2025-08-28 14:13:25.578 | DEBUG    | src.domain.ui_task.mobile.android.action_tool:_normalize_coordinates:101 - 🎯 坐标归一化: (75, 73) -> (90, 194) (屏幕尺寸: 1200x2670)
2025-08-28 14:13:25.578 | INFO     | src.domain.ui_task.mobile.android.image_processor:parse_coordinates_and_direction_from_action:209 - 📍 Parsed coordinates from 'click(point=<point>75 73</point>)': (90, 194)
2025-08-28 14:13:25.578 | INFO     | src.domain.ui_task.mobile.android.image_processor:annotate_screenshot_from_action:346 - 📍 Parsed coordinates from 'click(point=<point>75 73</point>)': (90, 194), direction=None
2025-08-28 14:13:25.653 | INFO     | src.domain.ui_task.mobile.android.image_processor:_draw_enhanced_marker:107 - 📍 Enhanced marker drawn at (90, 194) with outer_radius=57, direction=None
2025-08-28 14:13:26.031 | INFO     | src.domain.ui_task.mobile.android.image_processor:annotate_screenshot_with_coordinates:314 - 📸 Screenshot annotated in place: 20250828/c914844f201142b3af4e240dc4756b9e/screenshot_1_20250828_141314_971.png, direction=None
2025-08-28 14:13:26.032 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_annotate_screenshot_after_execution:749 - [c914844f201142b3af4e240dc4756b9e] 🎯 Screenshot annotated after reference execution: click(point=<point>75 73</point>)
2025-08-28 14:13:26.594 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:complete_task_action:369 - [action_id: 6597] ✅ Task action success: 6597
2025-08-28 14:13:26.595 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_complete_step_action_record:653 - Reference step action record completed: 成功 - 动作执行成功
2025-08-28 14:13:26.595 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:181 - [c914844f201142b3af4e240dc4756b9e] ✅ Execution result recorded to history
2025-08-28 14:13:26.600 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:327 - [c914844f201142b3af4e240dc4756b9e] 🎯 Executing reference-based step...
2025-08-28 14:13:26.600 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:346 - [c914844f201142b3af4e240dc4756b9e] 📋 Found 9 steps, current step: 1
2025-08-28 14:13:26.600 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_reference_step_node:357 - [c914844f201142b3af4e240dc4756b9e] 🎯 Executing step 1/9: 1.点击页面上方'首页'分类
2025-08-28 14:13:26.600 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_supervisor_check:424 - [c914844f201142b3af4e240dc4756b9e] 🔍 Executing supervisor check...
2025-08-28 14:13:26.601 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:51 - [task_id: c914844f201142b3af4e240dc4756b9e] 🔍 First execution, performing complete checks
2025-08-28 14:13:26.601 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell echo 'test'
2025-08-28 14:13:26.637 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:is_device_available:165 - ✅ Device 4f7d025f is responsive
2025-08-28 14:13:26.637 | DEBUG    | src.domain.ui_task.mobile.android.adb_connection_manager:is_device_available:180 - [c914844f201142b3af4e240dc4756b9e] ✅ Device 4f7d025f is responsive
2025-08-28 14:13:26.637 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell pm list packages
2025-08-28 14:13:26.688 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:125 - [task_id: c914844f201142b3af4e240dc4756b9e] ℹ️ First execution with restart disabled, skipping app restart
2025-08-28 14:13:26.688 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell dumpsys window | grep mCurrentFocus
2025-08-28 14:13:26.740 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:supervise_execution:138 - [task_id: c914844f201142b3af4e240dc4756b9e] ✓ App com.yiyou.ga is in foreground
2025-08-28 14:13:26.740 | INFO     | src.domain.ui_task.mobile.aggregate.agent.supervisor_agent:_check_execution_limits:426 - [task_id: c914844f201142b3af4e240dc4756b9e] 📊 Supervisor check: 0/27 actions executed
2025-08-28 14:13:26.740 | INFO     | src.domain.reference_task.service.reference_task_service:_execute_supervisor_check:436 - [c914844f201142b3af4e240dc4756b9e] ✅ Supervisor check passed
2025-08-28 14:13:26.740 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:89 - [c914844f201142b3af4e240dc4756b9e] 🔄 Step attempt 2/8
2025-08-28 14:13:26.740 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:101 - [c914844f201142b3af4e240dc4756b9e] 🔄 Step attempt 2/7
2025-08-28 14:13:27.975 | INFO     | src.domain.ui_task.mobile.service.task_persistence_service:create_task_action:326 - [c914844f201142b3af4e240dc4756b9e] ✅ Task action created: c914844f201142b3af4e240dc4756b9e - 1.点击页面上方'首页'分类
2025-08-28 14:13:27.975 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:107 - [c914844f201142b3af4e240dc4756b9e] 🧠 Reference decision-execution agent analyzing step: 1.点击页面上方'首页'分类
2025-08-28 14:13:30.981 | INFO     | src.domain.reference_task.agent.reference_decision_agent:execute_step_with_reference:111 - [c914844f201142b3af4e240dc4756b9e] 📸 Taking fresh screenshot before decision...
2025-08-28 14:13:30.982 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell screencap -p /sdcard/temp_screenshot_141330.png
2025-08-28 14:13:31.532 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f pull /sdcard/temp_screenshot_141330.png reports/ui_agent_test/reports/20250828/c914844f201142b3af4e240dc4756b9e/screenshot_1_20250828_141330_982.png
2025-08-28 14:13:31.600 | DEBUG    | src.domain.ui_task.mobile.android.native_adb_utils:execute_adb_command:40 - Executing ADB command: adb -s 4f7d025f shell rm /sdcard/temp_screenshot_141330.png
2025-08-28 14:13:31.642 | INFO     | src.domain.ui_task.mobile.android.native_adb_utils:take_screenshot:278 - 📸 Screenshot saved: reports/ui_agent_test/reports/20250828/c914844f201142b3af4e240dc4756b9e/screenshot_1_20250828_141330_982.png
2025-08-28 14:13:31.642 | INFO     | src.domain.ui_task.mobile.android.screenshot_manager:take_screenshot:254 - [task_id: c914844f201142b3af4e240dc4756b9e] 📸 Screenshot saved: 20250828/c914844f201142b3af4e240dc4756b9e/screenshot_1_20250828_141330_982.png
2025-08-28 14:13:32.185 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_step_reference_screenshots:456 - Loaded 1 step reference images for step '1.点击页面上方'首页'分类' in task fd07c4dc50624f1694d1b89f974f02cd
2025-08-28 14:13:32.186 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_next_step_first_screenshot:485 - Getting next step first screenshot and info for: 2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
2025-08-28 14:13:32.752 | INFO     | src.domain.reference_task.agent.reference_decision_agent:_get_next_step_first_screenshot:500 - Successfully loaded next step info for: 2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方
INFO:     127.0.0.1:57186 - "GET /api/v1/ui-task/record/get?task_id=c914844f201142b3af4e240dc4756b9e HTTP/1.1" 200
INFO:     Shutting down

Process finished with exit code 137 (interrupted by signal 9:SIGKILL)
